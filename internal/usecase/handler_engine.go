package usecase

import (
	"fmt"
	"receiver-demo/internal/infrastructure"

	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

type HandlerEngine interface {
	GetHandlerFunctions() []models.HandlerFunc
}

type HandlerEngineImpl struct {
	oracleAdapter *infrastructure.OracleAdapter
}

func NewHandlerEngine(oracleAdapter *infrastructure.OracleAdapter) HandlerEngine {
	return &HandlerEngineImpl{
		oracleAdapter: oracleAdapter,
	}
}

func (e *HandlerEngineImpl) GetHandlerFunctions() []models.HandlerFunc {
	handlers := []models.HandlerFunc{}

	fmt.Printf("====================== GetHandlerFunctions")

	orderRepo := infrastructure.NewOrderRepo(e.oracleAdapter)
	orderEngine := NewOrderEngine(orderRepo)
	orderHandlers := orderEngine.GetHandlerFunctions()
	handlers = append(handlers, orderHandlers...)

	paymentRepo := infrastructure.NewPaymentRepo(e.oracleAdapter)
	paymentEngine := NewPaymentEngine(paymentRepo)
	paymentHandlers := paymentEngine.GetHandlerFunctions()
	handlers = append(handlers, paymentHandlers...)

	return handlers
}
