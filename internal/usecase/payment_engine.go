package usecase

import (
	"fmt"
	"receiver-demo/internal/domain"
	"receiver-demo/pkg/utils"

	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

var _ domain.IHandlerEngine = (*PaymentEngine)(nil)

type PaymentEngine struct {
	paymentRepo domain.IPaymentRepo
}

func NewPaymentEngine(paymentRepo domain.IPaymentRepo) *PaymentEngine {
	return &PaymentEngine{
		paymentRepo: paymentRepo,
	}
}

func (e *PaymentEngine) GetHandlerFunctions() []models.HandlerFunc {
	return []models.HandlerFunc{
		{
			ID:       "PaymentHandler",
			Type:     constant.Payment,
			Function: e.PaymentHandler,
		},
	}
}

func (e *PaymentEngine) PaymentHandler(msg models.KafkaMessage) error {
	payments := msg["Payment"].([]interface{})
	paymentMsg := payments[0].(map[string]any)

	orderCreatedAt := msg.GetOrderCreatedAt()

	mspPayment := paymentMsg["MspPayment"].(map[string]any)
	amount := utils.GetInt64FromMap(mspPayment, "N_AMOUNT")

	payment := &domain.Payment{
		ID:             paymentMsg["ID"].(string),
		OrderID:        paymentMsg["OrderID"].(string),
		OrderCreatedAt: orderCreatedAt,
		Amount:         amount,
	}

	if err := e.paymentRepo.Save(payment); err != nil {
		fmt.Printf("======================= faield to save payment %v", err)
		return err
	}

	return nil
}
