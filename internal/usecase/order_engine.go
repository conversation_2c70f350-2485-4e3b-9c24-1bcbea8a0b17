package usecase

import (
	"fmt"
	"receiver-demo/internal/domain"
	"receiver-demo/pkg/utils"

	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

var _ domain.IHandlerEngine = (*OrderEngine)(nil)

type OrderEngine struct {
	orderRepo domain.IOrderRepo
}

func NewOrderEngine(orderRepo domain.IOrderRepo) *OrderEngine {
	return &OrderEngine{
		orderRepo: orderRepo,
	}
}

func (e *OrderEngine) GetHandlerFunctions() []models.HandlerFunc {
	return []models.HandlerFunc{
		{
			ID:       "OrderHandler",
			Type:     constant.Order,
			Function: e.OrderHandler,
		},
	}
}

func (e *OrderEngine) OrderHandler(msg models.KafkaMessage) error {
	orderMsg := msg["Order"].(map[string]any)

	orderCreatedAt := msg.GetOrderCreatedAt()

	data := orderMsg["Data"].(map[string]any)
	order := &domain.Order{
		ID:         orderMsg["ID"].(string),
		CreatedAt:  orderCreatedAt,
		MerchantID: data["S_MERCHANT_ID"].(string),
		Amount:     utils.GetInt64FromMap(data, "N_AMOUNT"),
		State:      data["S_STATE"].(string),
	}

	if err := e.orderRepo.Save(order); err != nil {
		fmt.Printf("====================== faield to save order %v", err)
		return err
	}

	return nil
}
