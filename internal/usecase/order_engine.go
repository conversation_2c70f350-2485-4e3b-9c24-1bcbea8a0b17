package usecase

import (
	"fmt"
	"receiver-demo/internal/domain"

	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
	"github.com/go-viper/mapstructure/v2"
)

var _ domain.IHandlerEngine = (*OrderEngine)(nil)

type OrderEngine struct {
	orderRepo domain.IOrderRepo
}

func NewOrderEngine(orderRepo domain.IOrderRepo) *OrderEngine {
	return &OrderEngine{
		orderRepo: orderRepo,
	}
}

func (e *OrderEngine) GetHandlerFunctions() []models.HandlerFunc {
	return []models.HandlerFunc{
		{
			ID:       "OrderHandler",
			Type:     constant.Order,
			Function: e.OrderHandler,
		},
	}
}

func (e *OrderEngine) OrderHandler(msg models.KafkaMessage) error {
	// Method 1: Using mapstructure (Recommended)
	orderMsgRaw := msg["Order"].(map[string]any)
	var orderMsg domain.OrderMessage
	fmt.Printf("====================== failed to save order")
	if err := mapstructure.Decode(orderMsgRaw, &orderMsg); err != nil {
		return fmt.Errorf("failed to decode order message: %w", err)
	}

	orderCreatedAt := msg.GetOrderCreatedAt()

	order := &domain.Order{
		ID:         orderMsg.ID,
		CreatedAt:  orderCreatedAt,
		MerchantID: orderMsg.Data.MerchantID,
		Amount:     orderMsg.Data.Amount,
		State:      orderMsg.Data.State,
	}

	if err := e.orderRepo.Save(order); err != nil {
		fmt.Printf("====================== failed to save order %v", err)
		return err
	}

	return nil
}
