package domain

import "time"

type Order struct {
	ID         string    `db:"s_id"`
	CreatedAt  time.Time `db:"t_created_at"`
	MerchantID string    `db:"s_merchant_id"`
	State      string    `db:"s_state"`
	Amount     int64     `db:"n_amount"`
}

// OrderMessage represents the structure of Order message from Kafka
type OrderMessage struct {
	ID   string    `mapstructure:"ID"`
	Data OrderData `mapstructure:"Data"`
}

// OrderData represents the Data field in Order message
type OrderData struct {
	MerchantID string `mapstructure:"S_MERCHANT_ID"`
	Amount     int64  `mapstructure:"N_AMOUNT"`
	State      string `mapstructure:"S_STATE"`
}
