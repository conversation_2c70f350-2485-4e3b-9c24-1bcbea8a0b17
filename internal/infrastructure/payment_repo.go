package infrastructure

import (
	"fmt"
	"receiver-demo/internal/domain"
)

var _ domain.IPaymentRepo = (*PaymentRepo)(nil)

type PaymentRepo struct {
	oracleAdapter *OracleAdapter
}

func NewPaymentRepo(adapter *OracleAdapter) *PaymentRepo {
	return &PaymentRepo{oracleAdapter: adapter}
}

func (r *PaymentRepo) Save(payment *domain.Payment) error {
	query := `
		MERGE INTO RECEIVER_DEMO.TB_PAYMENT t
		USING (SELECT :1 AS s_id, :2 AS s_order_id, :3 AS t_order_created_at, :4 AS n_amount FROM dual) s
		ON (t.s_id = s.s_id)
		WHEN MATCHED THEN
			UPDATE SET
				t.s_order_id = s.s_order_id,
				t.t_order_created_at = s.t_order_created_at,
				t.n_amount = s.n_amount
		WHEN NOT MATCHED THEN
			INSERT (s_id, s_order_id, t_order_created_at, n_amount)
			VALUES (s.s_id, s.s_order_id, s.t_order_created_at, s.n_amount)
	`
	_, err := r.oracleAdapter.Db.Exec(
		query,
		payment.ID,
		payment.OrderID,
		payment.OrderCreatedAt,
		payment.Amount,
	)
	if err != nil {
		return fmt.Errorf("failed to save payment: %w", err)
	}
	return nil
}
