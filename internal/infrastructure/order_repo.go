package infrastructure

import (
	"fmt"
	"receiver-demo/internal/domain"
)

var _ domain.IOrderRepo = (*OrderRepo)(nil)

type OrderRepo struct {
	oracleAdapter *OracleAdapter
}

func NewOrderRepo(adapter *OracleAdapter) *OrderRepo {
	return &OrderRepo{oracleAdapter: adapter}
}

func (r *OrderRepo) Save(order *domain.Order) error {
	query := `
		MERGE INTO RECEIVER_DEMO.TB_ORDER t
		USING (SELECT :1 AS s_id, :2 AS t_created_at, :3 AS s_merchant_id, :4 AS s_state, :5 AS n_amount FROM dual) s
		ON (t.s_id = s.s_id)
		WHEN MATCHED THEN
			UPDATE SET
				t.t_created_at = s.t_created_at,
				t.s_merchant_id = s.s_merchant_id,
				t.s_state = s.s_state,
				t.n_amount = s.n_amount
		WHEN NOT MATCHED THEN
			INSERT (s_id, t_created_at, s_merchant_id, s_state, n_amount)
			VALUES (s.s_id, s.t_created_at, s.s_merchant_id, s.s_state, s.n_amount)
	`
	_, err := r.oracleAdapter.Db.Exec(
		query,
		order.ID,
		order.CreatedAt,
		order.MerchantID,
		order.State,
		order.Amount,
	)
	if err != nil {
		return fmt.Errorf("failed to save order: %w", err)
	}
	return nil
}
