package infrastructure

import (
	"database/sql"
	"fmt"

	_ "github.com/sijms/go-ora/v2"
)

type OracleAdapter struct {
	Db *sql.DB
}

func NewOracleAdapter(dsn string) (*OracleAdapter, error) {
	db, err := sql.Open("oracle", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to oracle: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping oracle: %w", err)
	}

	return &OracleAdapter{Db: db}, nil
}

func (a *OracleAdapter) Close() {
	a.Db.Close()
}
