#!/bin/bash

echo "🚀 Starting local test environment..."

# Step 1: Start Docker services
echo "📦 Starting Docker services..."
docker-compose -f docker-compose.test.yml up -d

# Step 2: Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Step 3: Check if services are running
echo "🔍 Checking service status..."
docker-compose -f docker-compose.test.yml ps

# Step 4: Create Kafka topic
echo "📝 Creating Kafka topic..."
docker exec -it $(docker-compose -f docker-compose.test.yml ps -q kafka) \
  kafka-topics --create --topic test-topic --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1

# Step 5: Setup Oracle tables (manual step)
echo "🗄️  Please run Oracle setup manually:"
echo "   1. Connect to Oracle: sqlplus onecredit/onecredit111@localhost:1521/XE"
echo "   2. Run: @scripts/setup_oracle_tables.sql"
echo ""

# Step 6: Instructions for testing
echo "✅ Environment is ready!"
echo ""
echo "🧪 To test:"
echo "   1. Run the app: APP_ENV=local go run cmd/main.go"
echo "   2. Send test messages: cd test && go run kafka_producer.go"
echo "   3. Check Kafka UI: http://localhost:8080"
echo ""
echo "🛑 To stop: docker-compose -f docker-compose.test.yml down"
