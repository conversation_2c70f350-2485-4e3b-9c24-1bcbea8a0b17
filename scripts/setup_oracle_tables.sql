-- Create schema if not exists
CREATE USER RECEIVER_DEMO IDENTIFIED BY receiver123 DEFAULT TABLESPACE USERS;
GRANT CONNECT, RESOURCE TO RECEIVER_DEMO;
GRA<PERSON> CREATE SESSION TO RECEIVER_DEMO;
<PERSON><PERSON><PERSON> UNLIMITED TABLESPACE TO RECEIVER_DEMO;

-- Connect as RECEIVER_DEMO user
-- CREATE TABLE TB_ORDER
CREATE TABLE RECEIVER_DEMO.TB_ORDER (
    s_id VARCHAR2(100) PRIMARY KEY,
    t_created_at TIMESTAMP,
    s_merchant_id VARCHAR2(100),
    s_state VARCHAR2(50),
    n_amount NUMBER(19,0)
);

-- CREATE TABLE TB_PAYMENT  
CREATE TABLE RECEIVER_DEMO.TB_PAYMENT (
    s_id VARCHAR2(100) PRIMARY KEY,
    s_order_id VARCHAR2(100),
    t_order_created_at TIMESTAMP,
    n_amount NUMBER(19,0)
);

-- Create indexes
CREATE INDEX idx_order_merchant ON RECEIVER_DEMO.TB_ORDER(s_merchant_id);
CREATE INDEX idx_order_state ON RECEIVER_DEMO.TB_ORDER(s_state);
CREATE INDEX idx_payment_order ON RECEIVER_DEMO.TB_PAYMENT(s_order_id);
