package test

import (
	"receiver-demo/internal/domain"
	"receiver-demo/internal/infrastructure"
	"receiver-demo/internal/usecase"
	"testing"
	"time"

	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

func TestOrderHandler(t *testing.T) {
	// Mock Oracle connection (you might want to use testcontainers)
	// For now, skip if no Oracle connection
	oraAdapter, err := infrastructure.NewOracleAdapter("oracle://test:test@localhost:1521/XE")
	if err != nil {
		t.Skip("Oracle not available for testing")
	}
	defer oraAdapter.Close()

	// Create handler
	orderRepo := infrastructure.NewOrderRepo(oraAdapter)
	orderEngine := usecase.NewOrderEngine(orderRepo)

	// Mock Kafka message
	mockMsg := models.KafkaMessage{
		"Order": map[string]any{
			"ID": "TEST_ORDER_001",
			"Data": map[string]any{
				"S_MERCHANT_ID": "MERCHANT_TEST",
				"N_AMOUNT":      int64(100000),
				"S_STATE":       "PENDING",
			},
		},
	}

	// Mock GetOrderCreatedAt method
	mockMsg["OrderCreatedAt"] = time.Now()

	// Test handler
	err = orderEngine.OrderHandler(mockMsg)
	if err != nil {
		t.Errorf("OrderHandler failed: %v", err)
	}
}

func TestPaymentHandler(t *testing.T) {
	// Similar test for Payment handler
	oraAdapter, err := infrastructure.NewOracleAdapter("oracle://test:test@localhost:1521/XE")
	if err != nil {
		t.Skip("Oracle not available for testing")
	}
	defer oraAdapter.Close()

	paymentRepo := infrastructure.NewPaymentRepo(oraAdapter)
	paymentEngine := usecase.NewPaymentEngine(paymentRepo)

	mockMsg := models.KafkaMessage{
		"Payment": []interface{}{
			map[string]any{
				"ID":      "TEST_PAYMENT_001",
				"OrderID": "TEST_ORDER_001",
				"MspPayment": map[string]any{
					"N_AMOUNT": int64(100000),
				},
			},
		},
	}

	mockMsg["OrderCreatedAt"] = time.Now()

	err = paymentEngine.PaymentHandler(mockMsg)
	if err != nil {
		t.Errorf("PaymentHandler failed: %v", err)
	}
}
