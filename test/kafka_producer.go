package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/twmb/franz-go/pkg/kgo"
)

type OrderMessage struct {
	Order OrderData `json:"Order"`
}

type OrderData struct {
	ID   string         `json:"ID"`
	Data OrderDataField `json:"Data"`
}

type OrderDataField struct {
	MerchantID string `json:"S_MERCHANT_ID"`
	Amount     int64  `json:"N_AMOUNT"`
	State      string `json:"S_STATE"`
}

type PaymentMessage struct {
	Payment []PaymentData `json:"Payment"`
}

type PaymentData struct {
	ID         string            `json:"ID"`
	OrderID    string            `json:"OrderID"`
	MspPayment PaymentDataField  `json:"MspPayment"`
}

type PaymentDataField struct {
	Amount int64 `json:"N_AMOUNT"`
}

func main() {
	// Kafka configuration
	brokers := []string{
		"10.36.141.18:9092",
		"10.36.141.19:9092", 
		"10.36.141.38:9092",
	}
	topic := "uat-db-112.EVENTS.OUTPUT_2"

	// Create Kafka client
	client, err := kgo.NewClient(
		kgo.SeedBrokers(brokers...),
	)
	if err != nil {
		log.Fatalf("Failed to create Kafka client: %v", err)
	}
	defer client.Close()

	// Test Order Message
	fmt.Println("Sending Order message...")
	if err := sendOrderMessage(client, topic); err != nil {
		log.Printf("Failed to send order message: %v", err)
	}

	// Wait a bit
	time.Sleep(2 * time.Second)

	// Test Payment Message
	fmt.Println("Sending Payment message...")
	if err := sendPaymentMessage(client, topic); err != nil {
		log.Printf("Failed to send payment message: %v", err)
	}

	fmt.Println("Messages sent successfully!")
}

func sendOrderMessage(client *kgo.Client, topic string) error {
	orderMsg := OrderMessage{
		Order: OrderData{
			ID: fmt.Sprintf("ORDER_%d", time.Now().Unix()),
			Data: OrderDataField{
				MerchantID: "MERCHANT_TEST_001",
				Amount:     150000,
				State:      "PENDING",
			},
		},
	}

	msgBytes, err := json.Marshal(orderMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal order message: %w", err)
	}

	record := &kgo.Record{
		Topic: topic,
		Value: msgBytes,
	}

	return client.ProduceSync(context.Background(), record).FirstErr()
}

func sendPaymentMessage(client *kgo.Client, topic string) error {
	paymentMsg := PaymentMessage{
		Payment: []PaymentData{
			{
				ID:      fmt.Sprintf("PAYMENT_%d", time.Now().Unix()),
				OrderID: fmt.Sprintf("ORDER_%d", time.Now().Unix()-10),
				MspPayment: PaymentDataField{
					Amount: 150000,
				},
			},
		},
	}

	msgBytes, err := json.Marshal(paymentMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal payment message: %w", err)
	}

	record := &kgo.Record{
		Topic: topic,
		Value: msgBytes,
	}

	return client.ProduceSync(context.Background(), record).FirstErr()
}
