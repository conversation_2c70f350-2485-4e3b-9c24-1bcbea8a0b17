{"version": "0.2.0", "configurations": [{"name": "Launch Go App (Production)", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/cmd/main.go", "cwd": "${workspaceFolder}", "env": {"APP_ENV": "localhost"}, "args": [], "showLog": true}, {"name": "Launch Go App (Local Docker)", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/cmd/main.go", "cwd": "${workspaceFolder}", "env": {"APP_ENV": "local"}, "args": [], "showLog": true}]}