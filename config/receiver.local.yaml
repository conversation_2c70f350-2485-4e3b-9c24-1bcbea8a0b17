postgres:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  dbname: "datahub"
  sslmode: "disable"
  max_open_conns: 50
  max_idle_conns: 15
  conn_max_lifetime: 60  # minutes
  conn_max_idle_time: 30 # minutes

kafka:
  brokers:
    - "localhost:9092"
  group_id: "datahub_receiver_local"
  paygate_topics:
    - "test-topic"

logger:
  level: "debug"

lock:
  expire_seconds: 60
  retry_interval_ms: 100

cronjob:
  schedule: "*/1 * * * *"  # Every minute for testing
