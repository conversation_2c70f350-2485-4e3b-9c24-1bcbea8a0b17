package config

import (
	"fmt"

	go_ora "github.com/sijms/go-ora/v2"
	"github.com/spf13/viper"
)

var config Config

// Config represents the application configuration
type Config struct {
	Oracle   OracleConfig   `mapstructure:"oracle"`
	Receiver ReceiverConfig `mapstructure:"receiver"`
}

// OracleConfig represents Oracle configuration
type OracleConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
}

// ReceiverConfig represents Receiver configuration
type ReceiverConfig struct {
	ConfigFile string `mapstructure:"config_file"`
}

func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	// Add multiple config paths to support different working directories
	viper.AddConfigPath("config")    // From project root
	viper.AddConfigPath("../config") // From cmd directory
	viper.AddConfigPath("./")        // Current directory
	viper.AddConfigPath(".")         // Current directory alternative

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("error reading config file: %w", err)
	}

	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return &config, nil
}

func (c *Config) GetOracleDSN() string {
	return go_ora.BuildUrl(c.Oracle.Host, c.Oracle.Port, c.Oracle.DBName, c.Oracle.User, c.Oracle.Password, nil)
}
