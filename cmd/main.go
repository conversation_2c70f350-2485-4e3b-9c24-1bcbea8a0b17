package main

import (
	"fmt"
	"os"
	"receiver-demo/config"
	"receiver-demo/internal/infrastructure"
	"receiver-demo/internal/usecase"

	receiver "git.onepay.vn/onepay/datahub-receiver"
)

func main() {
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Initialize Oracle adapter
	oraAdapter, err := infrastructure.NewOracleAdapter(cfg.GetOracleDSN())
	if err != nil {
		fmt.Printf("Failed to create Oracle adapter: %v", err)
		os.Exit(1)
	}
	defer oraAdapter.Close()

	handlerEngine := usecase.NewHandlerEngine(oraAdapter)
	handlers := handlerEngine.GetHandlerFunctions()

	receiver, err := receiver.New(cfg.Receiver.ConfigFile, handlers)
	if err != nil {
		fmt.Println("Failed to init:", err)
	}

	receiver.Start()
}
